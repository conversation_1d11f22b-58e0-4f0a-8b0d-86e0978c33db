# Setup Instructions for Nepali Calendar Widget

## Quick Setup for Android Studio

Since this project was bootstrapped programmatically, you'll need to complete a few setup steps in Android Studio:

### 1. Open the Project in Android Studio

1. Launch Android Studio
2. Select "Open an existing project"
3. Navigate to this directory and select it
4. Click "OK"

### 2. Gradle Wrapper Setup

The gradle wrapper jar file needs to be downloaded. Android Studio will handle this automatically:

1. When you open the project, Android Studio will detect the missing gradle wrapper
2. Click "OK" when prompted to download the Gradle wrapper
3. Alternatively, you can run this command in the terminal:
   ```bash
   gradle wrapper
   ```

### 3. Sync and Build

1. Android Studio should automatically start syncing the project
2. If not, click "Sync Now" in the notification bar
3. Wait for the sync to complete
4. Build the project: Build → Make Project (Ctrl+F9)

### 4. Run the App

1. Connect an Android device or start an emulator
2. Click the "Run" button (green triangle) or press Shift+F10
3. Select your target device
4. The app should install and launch

### 5. Test the Widget

1. After installing the app, go to your device's home screen
2. Long press on an empty area
3. Select "Widgets"
4. Find "Nepali Calendar Widget" in the list
5. Drag it to your home screen

## Project Features

### Main App Features:
- Interactive Nepali date picker
- Language toggle (English/Nepali)
- Date conversion between Nepali and English calendars
- Material3 design
- Option to show both Nepali and English dates

### Widget Features:
- Displays current Nepali date
- Shows day, month, and year in Nepali numerals
- Beautiful gradient background
- Auto-updates daily

## Troubleshooting

### Common Issues:

1. **Gradle sync fails**: 
   - Check internet connection
   - Try File → Invalidate Caches and Restart

2. **Build errors**:
   - Ensure you have Android SDK 24+ installed
   - Check that Java 11+ is being used

3. **Widget not appearing**:
   - Make sure the app is installed
   - Check that your launcher supports widgets
   - Try restarting the device

4. **Import errors in code**:
   - The Nepali Date Picker library should be automatically downloaded
   - If imports are red, try rebuilding the project

## Next Steps

Once the project is set up, you can:
- Customize the UI colors and themes
- Add more widget sizes
- Implement additional Nepali calendar features
- Add notifications for important Nepali dates
- Extend the widget with more information

## Dependencies Used

- Nepali Date Picker: `io.github.shivathapaa:nepali-date-picker-android:2.5.1`
- Jetpack Compose with Material3
- Glance for widgets
- Core library desugaring for older Android versions

The project is ready to run and should work out of the box once opened in Android Studio!
