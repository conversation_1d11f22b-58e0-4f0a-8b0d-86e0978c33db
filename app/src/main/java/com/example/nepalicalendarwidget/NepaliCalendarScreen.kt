package com.example.nepalicalendarwidget

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import io.github.shivathapaa.nepalidatepicker.calendar.NepaliDateConverter
import io.github.shivathapaa.nepalidatepicker.calendar.SimpleDate
import io.github.shivathapaa.nepalidatepicker.data.NepaliDateFormatStyle
import io.github.shivathapaa.nepalidatepicker.data.NepaliDateLocale
import io.github.shivathapaa.nepalidatepicker.data.NepaliDatePickerLang
import io.github.shivathapaa.nepalidatepicker.data.NameFormat
import io.github.shivathapaa.nepalidatepicker.ui.NepaliDatePicker
import io.github.shivathapaa.nepalidatepicker.ui.NepaliDatePickerWithEnglishDate
import io.github.shivathapaa.nepalidatepicker.ui.rememberNepaliDatePickerState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NepaliCalendarScreen(
    modifier: Modifier = Modifier
) {
    var showEnglishDate by remember { mutableStateOf(false) }
    var selectedLanguage by remember { mutableStateOf(NepaliDatePickerLang.ENGLISH) }
    
    // Get today's dates
    val todayNepali = NepaliDateConverter.todayNepaliSimpleDate
    val todayEnglish = NepaliDateConverter.todayEnglishSimpleDate
    
    // Create date picker state
    val datePickerState = rememberNepaliDatePickerState(
        initialSelectedDate = todayNepali,
        locale = NepaliDateLocale(
            language = selectedLanguage,
            dateFormat = NepaliDateFormatStyle.FULL,
            weekDayName = NameFormat.FULL,
            monthName = NameFormat.FULL
        )
    )

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Title
        Text(
            text = "Nepali Calendar",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Today's date display
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Today",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "Nepali: ${formatNepaliDate(todayNepali, selectedLanguage)}",
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                Text(
                    text = "English: ${formatEnglishDate(todayEnglish)}",
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        // Language toggle
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Language:")
            
            FilterChip(
                onClick = { selectedLanguage = NepaliDatePickerLang.ENGLISH },
                label = { Text("English") },
                selected = selectedLanguage == NepaliDatePickerLang.ENGLISH
            )
            
            FilterChip(
                onClick = { selectedLanguage = NepaliDatePickerLang.NEPALI },
                label = { Text("नेपाली") },
                selected = selectedLanguage == NepaliDatePickerLang.NEPALI
            )
        }
        
        // Date display toggle
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Show English dates:")
            Switch(
                checked = showEnglishDate,
                onCheckedChange = { showEnglishDate = it }
            )
        }
        
        // Date Picker
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            if (showEnglishDate) {
                NepaliDatePickerWithEnglishDate(
                    state = datePickerState,
                    modifier = Modifier.padding(16.dp)
                )
            } else {
                NepaliDatePicker(
                    state = datePickerState,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
        
        // Selected date display
        datePickerState.selectedDate?.let { selectedDate ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Selected Date",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Text(
                        text = "Nepali: ${formatNepaliDate(selectedDate, selectedLanguage)}",
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                    
                    // Convert to English date
                    val englishDate = NepaliDateConverter.convertNepaliToEnglish(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.dayOfMonth
                    )
                    
                    Text(
                        text = "English: ${englishDate.year}/${englishDate.month}/${englishDate.dayOfMonth}",
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

private fun formatNepaliDate(date: SimpleDate, language: NepaliDatePickerLang): String {
    return NepaliDateConverter.formatNepaliDate(
        date.year,
        date.month,
        date.dayOfMonth,
        dayOfWeek = 1, // Default day of week
        locale = NepaliDateLocale(
            language = language,
            dateFormat = NepaliDateFormatStyle.FULL,
            weekDayName = NameFormat.FULL,
            monthName = NameFormat.FULL
        )
    )
}

private fun formatEnglishDate(date: SimpleDate): String {
    return "${date.year}/${date.month}/${date.dayOfMonth}"
}
