package com.example.nepalicalendarwidget.widget

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.padding
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextAlign
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import io.github.shivathapaa.nepalidatepicker.calendar.NepaliDateConverter
import io.github.shivathapaa.nepalidatepicker.data.NepaliDateFormatStyle
import io.github.shivathapaa.nepalidatepicker.data.NepaliDateLocale
import io.github.shivathapaa.nepalidatepicker.data.NepaliDatePickerLang
import io.github.shivathapaa.nepalidatepicker.data.NameFormat

class NepaliDateWidget : GlanceAppWidget() {

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        provideContent {
            GlanceTheme {
                NepaliDateWidgetContent()
            }
        }
    }

    @Composable
    private fun NepaliDateWidgetContent() {
        // Get today's Nepali date
        val todayNepali = NepaliDateConverter.todayNepaliSimpleDate
        val todayNepaliCalendar = NepaliDateConverter.todayNepaliCalendar
        
        // Format the date
        val nepaliDateLocale = NepaliDateLocale(
            language = NepaliDatePickerLang.NEPALI,
            dateFormat = NepaliDateFormatStyle.FULL,
            weekDayName = NameFormat.FULL,
            monthName = NameFormat.FULL
        )
        
        val formattedDate = NepaliDateConverter.formatNepaliDate(
            todayNepaliCalendar,
            nepaliDateLocale
        )
        
        val monthName = NepaliDateConverter.getMonthName(
            todayNepali.month,
            NameFormat.FULL,
            NepaliDatePickerLang.NEPALI
        )

        Box(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(ColorProvider(Color(0xFF1976D2)))
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Day of month (large)
                Text(
                    text = todayNepali.dayOfMonth.toString().convertToNepaliNumber(),
                    style = TextStyle(
                        fontSize = 48.sp,
                        fontWeight = FontWeight.Bold,
                        color = ColorProvider(Color.White),
                        textAlign = TextAlign.Center
                    )
                )
                
                // Month name
                Text(
                    text = monthName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = ColorProvider(Color.White),
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.padding(top = 4.dp)
                )
                
                // Year
                Text(
                    text = todayNepali.year.toString().convertToNepaliNumber(),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Normal,
                        color = ColorProvider(Color.White),
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.padding(top = 2.dp)
                )
            }
        }
    }
}

class NepaliDateWidgetReceiver : GlanceAppWidgetReceiver() {
    override val glanceAppWidget: GlanceAppWidget = NepaliDateWidget()
}

// Extension function to convert English numbers to Nepali
private fun String.convertToNepaliNumber(): String {
    val englishToNepali = mapOf(
        '0' to '०', '1' to '१', '2' to '२', '3' to '३', '4' to '४',
        '5' to '५', '6' to '६', '7' to '७', '8' to '८', '9' to '९'
    )
    
    return this.map { char ->
        englishToNepali[char] ?: char
    }.joinToString("")
}
