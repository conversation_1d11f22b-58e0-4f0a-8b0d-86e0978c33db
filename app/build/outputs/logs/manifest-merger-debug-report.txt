-- Merging decision tree log ---
manifest
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:2:1-41:12
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:2:1-41:12
MERGED from [androidx.glance:glance:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/880275ae1718f463d172ea827efedbe6/transformed/glance-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.glance:glance-material3:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/0fdee8d80be59d7515660ce022956068/transformed/glance-material3-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:17:1-61:12
MERGED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:17:1-30:12
MERGED from [io.github.shivathapaa:nepali-date-picker-android:2.5.1] /home/<USER>/.gradle/caches/8.9/transforms/4cd9822ccc431fbfb9cfe844cfb6c442/transformed/nepali-date-picker-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/daa2ac754d8e809cb59c759e44e132b1/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/07a6deeb2983bd6b97bd76599e398397/transformed/ui-backhandler-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/886decc99a0b2d9337caef73627ea43b/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/c3f965c01061827867d5197a0c9a53aa/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/a615882ed6014e793efbf8daebeb0bde/transformed/material-ripple/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/91bde2353697b2245e30f8b895b25008/transformed/animation/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/defd4b6f4ffabbde8dff4b032757841e/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/47c8cfdf23103dec51224a62dead4fe0/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/e8fc5138a52b49e01c84eca7e5f72e9e/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/28a9ec9febcaca7ee71d8f8c8e49535c/transformed/ui-tooling-data/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/30f581079007b96b4d6b0b609bfe3d97/transformed/ui-tooling-preview/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/2982b24fd5c910397da22380882bd9ff/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/930f0dcfac6c5f9a8ac37c8c5216a0d0/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/96edca8a0d9709b667732582c775b143/transformed/ui-geometry/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:17:1-29:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/7d096e233eb8b1218b377b3ebc95734e/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/392c24f95cb33e13e0cf996555ff5ce0/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/136fa1eebb35e3992e522dfbc53b9ae0/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/4ccd77416da44f455f6c537fb93c1839/transformed/activity-compose-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/85ecc4782d648ce7abfa0f18fc6f3f91/transformed/activity-ktx-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/5667f8cfe744fd74576334818b9d4752/transformed/activity-1.9.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/abb8155dd497b8360df2c7b1401f45b5/transformed/work-runtime-ktx-2.7.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.autofill:autofill:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/b53993f5cd6f9a31b1512c101de808ce/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.graphics:graphics-path:1.0.1] /home/<USER>/.gradle/caches/8.9/transforms/2991f5ed7299cf8439061b81c63bd90f/transformed/graphics-path-1.0.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/415dfcc828604cdc5eea8a8cca9b5c0d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/226af5c113fe883dca4cb9e85772f62a/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5be660bff2a878c7b1207967ada70c7f/transformed/lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5b7cc5bf54bd13075910ccb0f10b79e2/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/e5da409011430ae4f6c246e36d1370be/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/8651121e6edd7b399a2ba261744f18a6/transformed/lifecycle-service-2.8.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/c3d5efc5c5ef5fd5bd6daf9d540cbb84/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/aa150cf246c18f67968674e24accd0f7/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5f2fbac4d5b4447498e771b0a315ff18/transformed/lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/9f18528294630d0d068b01f63809f74c/transformed/lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/2c5895654e8cc575bf6e8935050c8fc4/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/52040df4e0f1d08056661ce2c633175a/transformed/lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/2247ecc07bbd4d7f9076c0130a5fd5bc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/1fa85866e834700cbc64a4fcdd261df3/transformed/core-ktx-1.15.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/42c1c522cd2ea1fc9041cc1df74ca114/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/0ff44e145bedce72d900aaeaaf989666/transformed/datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/177a3ffbd91351b30b26b7eebc15baaa/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/992e2964613e2d8fdac8f1df355cd62c/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /home/<USER>/.gradle/caches/8.9/transforms/0995ad634dd2a503e9172c9605a37ea1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f447e97ba7c06e49ba0dfab6408f9cf4/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/37a3a7a02821618071d9cbdbca5ac92d/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/7ec52a27c7e921e55cddc4768b000aea/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/7f93efcefbf24edd6c9fd585144604de/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/5e51a9cde6950da2cdbcd84a573ebd91/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/4617ad3a33a5cf5cea27e948f82cc478/transformed/sqlite-2.1.0/AndroidManifest.xml:17:1-24:12
	package
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:2:11-69
application
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:5:5-39:19
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:5:5-39:19
MERGED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:22:5-59:19
MERGED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:22:5-59:19
MERGED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:23:5-28:19
MERGED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:23:5-28:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/7ec52a27c7e921e55cddc4768b000aea/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/7ec52a27c7e921e55cddc4768b000aea/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:12:9-35
	android:label
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:14:9-29
	android:icon
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:6:9-35
	android:theme
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:13:9-58
	android:dataExtractionRules
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:7:9-65
activity#com.example.nepalicalendarwidget.MainActivity
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:16:9-25:20
	android:label
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:19:13-45
	android:exported
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:18:13-36
	android:theme
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:20:13-62
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:21:13-24:29
action#android.intent.action.MAIN
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:22:17-69
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:23:17-77
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:23:27-74
receiver#com.example.nepalicalendarwidget.widget.NepaliDateWidgetReceiver
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:28:9-37:20
	android:exported
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:30:13-36
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:29:13-60
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:31:13-33:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:32:17-84
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:32:25-81
meta-data#android.appwidget.provider
ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:34:13-36:67
	android:resource
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:36:17-64
	android:name
		ADDED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml:35:17-58
uses-sdk
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
MERGED from [androidx.glance:glance:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/880275ae1718f463d172ea827efedbe6/transformed/glance-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/880275ae1718f463d172ea827efedbe6/transformed/glance-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-material3:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/0fdee8d80be59d7515660ce022956068/transformed/glance-material3-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-material3:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/0fdee8d80be59d7515660ce022956068/transformed/glance-material3-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:21:5-44
MERGED from [io.github.shivathapaa:nepali-date-picker-android:2.5.1] /home/<USER>/.gradle/caches/8.9/transforms/4cd9822ccc431fbfb9cfe844cfb6c442/transformed/nepali-date-picker-release/AndroidManifest.xml:5:5-44
MERGED from [io.github.shivathapaa:nepali-date-picker-android:2.5.1] /home/<USER>/.gradle/caches/8.9/transforms/4cd9822ccc431fbfb9cfe844cfb6c442/transformed/nepali-date-picker-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/daa2ac754d8e809cb59c759e44e132b1/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/daa2ac754d8e809cb59c759e44e132b1/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/07a6deeb2983bd6b97bd76599e398397/transformed/ui-backhandler-debug/AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/07a6deeb2983bd6b97bd76599e398397/transformed/ui-backhandler-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/886decc99a0b2d9337caef73627ea43b/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/886decc99a0b2d9337caef73627ea43b/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/c3f965c01061827867d5197a0c9a53aa/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/c3f965c01061827867d5197a0c9a53aa/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/a615882ed6014e793efbf8daebeb0bde/transformed/material-ripple/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/a615882ed6014e793efbf8daebeb0bde/transformed/material-ripple/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/91bde2353697b2245e30f8b895b25008/transformed/animation/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/91bde2353697b2245e30f8b895b25008/transformed/animation/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/defd4b6f4ffabbde8dff4b032757841e/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/defd4b6f4ffabbde8dff4b032757841e/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/47c8cfdf23103dec51224a62dead4fe0/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/47c8cfdf23103dec51224a62dead4fe0/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/e8fc5138a52b49e01c84eca7e5f72e9e/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] /home/<USER>/.gradle/caches/8.9/transforms/e8fc5138a52b49e01c84eca7e5f72e9e/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/28a9ec9febcaca7ee71d8f8c8e49535c/transformed/ui-tooling-data/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/28a9ec9febcaca7ee71d8f8c8e49535c/transformed/ui-tooling-data/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/30f581079007b96b4d6b0b609bfe3d97/transformed/ui-tooling-preview/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/30f581079007b96b4d6b0b609bfe3d97/transformed/ui-tooling-preview/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/2982b24fd5c910397da22380882bd9ff/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/2982b24fd5c910397da22380882bd9ff/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/930f0dcfac6c5f9a8ac37c8c5216a0d0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/930f0dcfac6c5f9a8ac37c8c5216a0d0/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/96edca8a0d9709b667732582c775b143/transformed/ui-geometry/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/96edca8a0d9709b667732582c775b143/transformed/ui-geometry/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/7d096e233eb8b1218b377b3ebc95734e/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/7d096e233eb8b1218b377b3ebc95734e/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/392c24f95cb33e13e0cf996555ff5ce0/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/392c24f95cb33e13e0cf996555ff5ce0/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/136fa1eebb35e3992e522dfbc53b9ae0/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/136fa1eebb35e3992e522dfbc53b9ae0/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/4ccd77416da44f455f6c537fb93c1839/transformed/activity-compose-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/4ccd77416da44f455f6c537fb93c1839/transformed/activity-compose-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/85ecc4782d648ce7abfa0f18fc6f3f91/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/85ecc4782d648ce7abfa0f18fc6f3f91/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/5667f8cfe744fd74576334818b9d4752/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] /home/<USER>/.gradle/caches/8.9/transforms/5667f8cfe744fd74576334818b9d4752/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/abb8155dd497b8360df2c7b1401f45b5/transformed/work-runtime-ktx-2.7.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/abb8155dd497b8360df2c7b1401f45b5/transformed/work-runtime-ktx-2.7.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.autofill:autofill:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/b53993f5cd6f9a31b1512c101de808ce/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/b53993f5cd6f9a31b1512c101de808ce/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /home/<USER>/.gradle/caches/8.9/transforms/2991f5ed7299cf8439061b81c63bd90f/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /home/<USER>/.gradle/caches/8.9/transforms/2991f5ed7299cf8439061b81c63bd90f/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/415dfcc828604cdc5eea8a8cca9b5c0d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/415dfcc828604cdc5eea8a8cca9b5c0d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/226af5c113fe883dca4cb9e85772f62a/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/226af5c113fe883dca4cb9e85772f62a/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5be660bff2a878c7b1207967ada70c7f/transformed/lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5be660bff2a878c7b1207967ada70c7f/transformed/lifecycle-livedata-core-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5b7cc5bf54bd13075910ccb0f10b79e2/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5b7cc5bf54bd13075910ccb0f10b79e2/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/e5da409011430ae4f6c246e36d1370be/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/e5da409011430ae4f6c246e36d1370be/transformed/lifecycle-livedata-core-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/8651121e6edd7b399a2ba261744f18a6/transformed/lifecycle-service-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/8651121e6edd7b399a2ba261744f18a6/transformed/lifecycle-service-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/c3d5efc5c5ef5fd5bd6daf9d540cbb84/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/c3d5efc5c5ef5fd5bd6daf9d540cbb84/transformed/lifecycle-livedata-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/aa150cf246c18f67968674e24accd0f7/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/aa150cf246c18f67968674e24accd0f7/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5f2fbac4d5b4447498e771b0a315ff18/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/5f2fbac4d5b4447498e771b0a315ff18/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/9f18528294630d0d068b01f63809f74c/transformed/lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/9f18528294630d0d068b01f63809f74c/transformed/lifecycle-viewmodel-ktx-2.8.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/2c5895654e8cc575bf6e8935050c8fc4/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/2c5895654e8cc575bf6e8935050c8fc4/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/52040df4e0f1d08056661ce2c633175a/transformed/lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/52040df4e0f1d08056661ce2c633175a/transformed/lifecycle-viewmodel-savedstate-2.8.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/2247ecc07bbd4d7f9076c0130a5fd5bc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/2247ecc07bbd4d7f9076c0130a5fd5bc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/1fa85866e834700cbc64a4fcdd261df3/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/1fa85866e834700cbc64a4fcdd261df3/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/42c1c522cd2ea1fc9041cc1df74ca114/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/42c1c522cd2ea1fc9041cc1df74ca114/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/0ff44e145bedce72d900aaeaaf989666/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/0ff44e145bedce72d900aaeaaf989666/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/177a3ffbd91351b30b26b7eebc15baaa/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/177a3ffbd91351b30b26b7eebc15baaa/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/992e2964613e2d8fdac8f1df355cd62c/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/992e2964613e2d8fdac8f1df355cd62c/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /home/<USER>/.gradle/caches/8.9/transforms/0995ad634dd2a503e9172c9605a37ea1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /home/<USER>/.gradle/caches/8.9/transforms/0995ad634dd2a503e9172c9605a37ea1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f447e97ba7c06e49ba0dfab6408f9cf4/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f447e97ba7c06e49ba0dfab6408f9cf4/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/37a3a7a02821618071d9cbdbca5ac92d/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/37a3a7a02821618071d9cbdbca5ac92d/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/7ec52a27c7e921e55cddc4768b000aea/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/7ec52a27c7e921e55cddc4768b000aea/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/7f93efcefbf24edd6c9fd585144604de/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/7f93efcefbf24edd6c9fd585144604de/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/5e51a9cde6950da2cdbcd84a573ebd91/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/5e51a9cde6950da2cdbcd84a573ebd91/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/4617ad3a33a5cf5cea27e948f82cc478/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /home/<USER>/.gradle/caches/8.9/transforms/4617ad3a33a5cf5cea27e948f82cc478/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /home/<USER>/github/nepali_calendar_widget/app/src/main/AndroidManifest.xml
activity#androidx.glance.appwidget.action.ActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:23:9-28:62
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:25:13-35
	android:excludeFromRecents
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:26:13-46
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:28:13-59
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:24:13-85
activity#androidx.glance.appwidget.action.InvisibleActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:29:9-36:81
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:31:13-35
	android:launchMode
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:33:13-48
	android:noHistory
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:34:13-37
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:32:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:36:13-78
	android:taskAffinity
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:35:13-91
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:30:13-94
receiver#androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:41:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:39:13-92
receiver#androidx.glance.appwidget.UnmanagedSessionReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:42:9-45:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:44:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:45:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:43:13-78
receiver#androidx.glance.appwidget.MyPackageReplacedReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:46:9-53:20
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:48:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:49:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:47:13-79
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:50:13-52:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:51:17-84
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:51:25-81
service#androidx.glance.appwidget.GlanceRemoteViewsService
ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:55:9-58:72
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:57:13-36
	android:permission
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:58:13-69
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/8fce653c28f0d6b656296e4682240770/transformed/glance-appwidget-1.1.0/AndroidManifest.xml:56:13-78
service#androidx.core.widget.RemoteViewsCompatService
ADDED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:24:9-27:63
	tools:ignore
		ADDED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:27:13-60
	android:permission
		ADDED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:26:13-69
	android:name
		ADDED from [androidx.core:core-remoteviews:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/612146bfc5944e60cdf57207e85fc5bc/transformed/core-remoteviews-1.1.0/AndroidManifest.xml:25:13-73
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/f7784696ddba5a04520494761793ff6f/transformed/ui-test-manifest-1.8.0/AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/38e3c916e1ee8fa827f6819cd804044f/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:25:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:26:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:27:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:27:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/6f5912417cf340820148a752d4a18968/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /home/<USER>/.gradle/caches/8.9/transforms/36f293230387c8040c97d60b2ccd9d0d/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/89ed7332cb64166e677c308c3443618f/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
permission#com.example.nepalicalendarwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
uses-permission#com.example.nepalicalendarwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.9/transforms/a0121496ab9ecffa778a008f95128b04/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] /home/<USER>/.gradle/caches/8.9/transforms/cd28063bf21edfa4c33b6091a6d9ebce/transformed/lifecycle-process-2.8.7/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /home/<USER>/.gradle/caches/8.9/transforms/8b162003ad8f294b8ed17a0bc1f0e6f3/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] /home/<USER>/.gradle/caches/8.9/transforms/380ec2a4b5fe42717f89596c57f6c160/transformed/room-runtime-2.2.5/AndroidManifest.xml:26:13-74
