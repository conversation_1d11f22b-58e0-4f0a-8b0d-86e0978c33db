# Nepali Calendar Widget

An Android application built with Jetpack Compose that provides a beautiful Nepali calendar interface and a home screen widget displaying the current Nepali date.

## Features

### Main Application

- **Interactive Nepali Date Picker**: Full-featured date picker using the [Nepali Date Picker library](https://github.com/shivathapaa/Nepali-Date-Picker)
- **Dual Language Support**: Switch between English and Nepali (नेपाली) languages
- **Date Conversion**: Seamless conversion between Nepali and English dates
- **Material3 Design**: Modern UI following Material Design 3 principles
- **Both Calendar Views**: Option to show Nepali dates only or both Nepali and English dates

### Home Screen Widget

- **Current Nepali Date Display**: Shows today's Nepali date in beautiful Nepali numerals
- **Compact Design**: Fits perfectly on your home screen
- **Auto-updating**: Updates daily to show the current date
- **Nepali Typography**: Displays month names and dates in Nepali script

## Setup Instructions

### Prerequisites

- Android Studio (latest version recommended)
- Android SDK with API level 24 or higher
- Java 11 or higher

### Building the Project

1. **Clone or download this project** to your local machine

2. **Open Android Studio** and select "Open an existing project"

3. **Navigate to the project directory** and open it

4. **Sync the project** - Android Studio should automatically sync the Gradle files. If not, click "Sync Now" when prompted.

5. **Build the project** by selecting Build → Make Project or pressing Ctrl+F9 (Cmd+F9 on Mac)

6. **Run the app** by clicking the Run button or pressing Shift+F10 (Ctrl+R on Mac)

### Installing the Widget

1. **Install the app** on your device
2. **Long press on your home screen** to enter widget mode
3. **Select "Widgets"** from the options
4. **Find "Nepali Calendar Widget"** in the widget list
5. **Drag and drop** the widget to your desired location on the home screen

## Project Structure

```
app/
├── src/main/
│   ├── java/com/example/nepalicalendarwidget/
│   │   ├── MainActivity.kt                 # Main activity
│   │   ├── NepaliCalendarScreen.kt        # Main calendar screen
│   │   ├── ui/theme/                      # Theme files
│   │   └── widget/
│   │       └── NepaliDateWidget.kt        # Widget implementation
│   ├── res/
│   │   ├── layout/
│   │   │   └── nepali_date_widget.xml     # Widget layout
│   │   ├── xml/
│   │   │   └── nepali_date_widget_info.xml # Widget configuration
│   │   └── values/
│   │       ├── strings.xml                # String resources
│   │       └── themes.xml                 # App themes
│   └── AndroidManifest.xml               # App manifest
├── build.gradle.kts                      # App-level build configuration
└── proguard-rules.pro                    # ProGuard rules
```

## Dependencies

- **Nepali Date Picker**: `io.github.shivathapaa:nepali-date-picker-android:2.5.1`
- **Jetpack Compose**: Latest stable version
- **Material3**: For modern UI components
- **Glance**: For widget implementation
- **Core Library Desugaring**: For API level compatibility

## Key Features Explained

### Date Conversion

The app uses the Nepali Date Picker library which provides:

- Accurate conversion between Nepali (Bikram Sambat) and English (Gregorian) calendars
- Support for date ranges from 1970 to 2100
- Proper handling of leap years and month variations

### Widget Implementation

The widget is built using:

- **Glance**: Google's modern widget framework
- **Automatic Updates**: Updates daily at midnight
- **Responsive Design**: Adapts to different widget sizes
- **Nepali Numerals**: Displays dates in authentic Nepali script

### Language Support

- **English Interface**: Standard English month names and numerals
- **Nepali Interface**: Complete Nepali localization including:
  - Nepali month names (बैशाख, जेठ, असार, etc.)
  - Nepali numerals (१, २, ३, etc.)
  - Nepali day names (आइतबार, सोमबार, etc.)

## Troubleshooting

### Build Issues

- **Gradle Sync Failed**: Try File → Invalidate Caches and Restart
- **Dependency Issues**: Ensure you have the latest Android SDK and build tools
- **Compilation Errors**: Check that you're using Java 11 or higher

### Widget Issues

- **Widget Not Appearing**: Check that the app is installed and permissions are granted
- **Widget Not Updating**: Try removing and re-adding the widget
- **Display Issues**: Ensure your launcher supports modern widgets

## Contributing

Feel free to contribute to this project by:

- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## Credits

- **Nepali Date Picker Library**: [shivathapaa/Nepali-Date-Picker](https://github.com/shivathapaa/Nepali-Date-Picker)
- **Material Design**: Google's Material Design 3
- **Jetpack Compose**: Google's modern UI toolkit

## License

This project is open source. Please check the individual dependencies for their respective licenses.
